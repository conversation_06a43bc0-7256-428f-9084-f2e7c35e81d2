import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_storage/get_storage.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../core/utils/app_constants.dart';
import '../../../../../core/widgets/universal_loading.dart';
import '../../../../../di/dependency_injection.dart';
import '../../../../../translations/locale_keys.g.dart';
import '../bloc/sot_empty_places_bloc.dart';
import '../widgets/empty_place_card.dart';
import '../widgets/empty_place_shimmer.dart';
import '../widgets/bottom_loading_widget.dart';
import '../../models/empty_place_model.dart';

class SotEmptyPlacesPage extends StatelessWidget {
  const SotEmptyPlacesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di<SotEmptyPlacesBloc>(),
      child: const _SotEmptyPlacesView(),
    );
  }
}

class _SotEmptyPlacesView extends StatefulWidget {
  const _SotEmptyPlacesView();

  @override
  State<_SotEmptyPlacesView> createState() => _SotEmptyPlacesViewState();
}

class _SotEmptyPlacesViewState extends State<_SotEmptyPlacesView> {
  late ScrollController _scrollController;
  final GetStorage _storage = GetStorage();
  static const String _defaultMarketId = '';

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    _loadFreePlaces();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle scroll events for pagination
  void _onScroll() {
    if (_isBottom) {
      final marketId = _storage.read(MARKET_ID) ?? _defaultMarketId;
      context.read<SotEmptyPlacesBloc>().add(LoadMoreFreePlaces(marketId: marketId));
    }
  }

  /// Check if user has scrolled to bottom
  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9); // Trigger at 90% scroll
  }

  void _loadFreePlaces() {
    // Get market ID from storage or use default
    final marketId = _storage.read(MARKET_ID) ?? _defaultMarketId;
    context.read<SotEmptyPlacesBloc>().add(LoadFreePlaces(marketId: marketId));
  }

  /// Handle refresh
  Future<void> _onRefresh() async {
    // Provide haptic feedback for better user experience
    HapticFeedback.lightImpact();

    // Add a small delay to show the refresh indicator
    await Future.delayed(const Duration(milliseconds: 300));
    final marketId = _storage.read(MARKET_ID) ?? _defaultMarketId;
    if (mounted) {
      context.read<SotEmptyPlacesBloc>().add(RefreshFreePlaces(marketId: marketId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        centerTitle: false,
        title: Text(
          LocaleKeys.navigation_empty_places.tr(),
          style: AppTextStyles.titleLarge.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: BlocConsumer<SotEmptyPlacesBloc, SotEmptyPlacesState>(
        listener: (context, state) {
          // Handle refresh errors with toast messages
          // Show toast when there's a refresh error and existing data
          if (state.hasRefreshError && state.hasAnyEmptyPlaces && state.message != null) {
            // This is a refresh error - show toast while keeping existing content
            final message = state.isNetworkError
                ? LocaleKeys.sotuvchi_empty_places_network_error.tr()
                : state.message!;

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(message),
                backgroundColor: Theme.of(context).colorScheme.error,
                duration: const Duration(seconds: 3),
                action: state.isNetworkError ? SnackBarAction(
                  label: LocaleKeys.common_retry.tr(),
                  textColor: Theme.of(context).colorScheme.onError,
                  onPressed: () {
                    final marketId = _storage.read(MARKET_ID) ?? _defaultMarketId;
                    context.read<SotEmptyPlacesBloc>().add(
                      RefreshFreePlaces(marketId: marketId),
                    );
                  },
                ) : null,
              ),
            );
          }
        },
        builder: (context, state) {
          return RefreshIndicator(
            onRefresh: _onRefresh,
            color: Theme.of(context).colorScheme.primary,
            backgroundColor: Theme.of(context).colorScheme.surface,
            child: _buildContent(state),
          );
        },
      ),
    );
  }

  /// Build content based on state
  Widget _buildContent(SotEmptyPlacesState state) {
    // Show shimmer during initial loading or when refreshing (always show shimmer during refresh)
    if (state.isLoading || state.isInitial || state.isRefreshing) {
      return const EmptyPlaceShimmer();
    }

    // Handle initial load errors - show centered error widget only if no existing data
    if (state.isFailure && !state.hasAnyEmptyPlaces) {
      return _buildErrorState(state);
    }

    if (state.isSuccess || state.hasAnyEmptyPlaces) {
      return _buildEmptyPlacesList(state);
    }

    return const SizedBox.shrink();
  }

  /// Build empty places list
  Widget _buildEmptyPlacesList(SotEmptyPlacesState state) {
    if (state.emptyPlaces.isEmpty) {
      return _buildEmptyStateScrollable();
    }

    return ListView.separated(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: state.emptyPlaces.length + (state.isLoadingMore ? 1 : 0),
      physics: const AlwaysScrollableScrollPhysics(),
      separatorBuilder: (context, index) {
        // Don't add separator after the last item if loading more
        if (index == state.emptyPlaces.length - 1 && state.isLoadingMore) {
          return const SizedBox.shrink();
        }
        return const SizedBox(height: 12);
      },
      itemBuilder: (context, index) {
        // Show loading widget at the bottom
        if (index == state.emptyPlaces.length) {
          return const BottomLoadingWidget();
        }

        final place = state.emptyPlaces[index];
        return EmptyPlaceCard(
          imageUrl: place.imageUrl ?? '',
          blockNumber: place.block.title,
          rastaNumber: place.displayRastaNumber,
          status: place.pavilion.title,
          price: place.displayPrice,
          onTap: () {
            _onPlaceTap(place);
          },
        );
      },
    );
  }

  /// Build empty state (scrollable for RefreshIndicator)
  Widget _buildEmptyStateScrollable() {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.8,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.store_outlined,
                  size: 64,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                SizedBox(height: 16),
                Text(
                  LocaleKeys.places_no_empty_places.tr(),
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontSize: 16,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  LocaleKeys.places_no_empty_places_subtitle.tr(),
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build error state
  Widget _buildErrorState(SotEmptyPlacesState state) {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        Container(
          height: MediaQuery.of(context).size.height * 0.8,
          child: Center(
            child: UniversalLoading.error(
              message: state.isNetworkError
                  ? LocaleKeys.sotuvchi_empty_places_network_error.tr()
                  : (state.message ?? LocaleKeys.sotuvchi_empty_places_data_load_error.tr()),
              onRetry: () {
                _loadFreePlaces();
              },
              retryButtonText: LocaleKeys.common_retry.tr(),
              icon: state.isNetworkError ? Icons.wifi_off : null,
            ),
          ),
        ),
      ],
    );
  }

  void _onPlaceTap(EmptyPlace place) {
    // Handle place tap - navigate to details or show more info
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20.r),
        ),
      ),
      builder: (context) => _buildPlaceDetailsBottomSheet(place),
    );
  }

  Widget _buildPlaceDetailsBottomSheet(EmptyPlace place) {
    return Container(
      padding: EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          SizedBox(height: 24),

          Text(
            '${place.block.title}, ${LocaleKeys.places_place_number.tr()} #${place.displayRastaNumber}',
            style: AppTextStyles.titleLarge.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16),
          _buildDetailRow(LocaleKeys.places_category.tr(), place.pavilion.title),
          SizedBox(height: 12),
          _buildDetailRow(LocaleKeys.places_rental_price.tr(), place.displayPrice),
          SizedBox(height: 24),

          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // Handle contact or booking action
                _showContactDialog();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                LocaleKeys.places_contact.tr(),
                style: AppTextStyles.buttonText.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontSize: 14,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  void _showContactDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          LocaleKeys.places_contact.tr(),
          style: AppTextStyles.titleMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          LocaleKeys.places_contact_message.tr(),
          style: AppTextStyles.bodyMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              LocaleKeys.common_close.tr(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Handle call action
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              LocaleKeys.places_call.tr(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).colorScheme.onPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
